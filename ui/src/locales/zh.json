{"common": {"yes": "是", "no": "否"}, "app": {"title": "Claude <PERSON> Router", "save": "保存", "save_and_restart": "保存并重启", "cancel": "取消", "edit": "编辑", "remove": "移除", "delete": "删除", "settings": "设置", "selectFile": "选择文件", "config_saved_success": "配置保存成功", "config_saved_failed": "配置保存失败", "config_saved_restart_success": "配置保存并服务重启成功", "config_saved_restart_failed": "配置保存并服务重启失败", "new_version_available": "有新版本可用", "update_description": "发现新版本。请查看更新日志并更新以获取最新功能和改进。", "no_changelog_available": "暂无更新日志", "later": "稍后再说", "update_now": "立即更新", "no_updates_available": "当前已是最新版本", "update_check_failed": "检查更新失败", "update_successful": "更新成功", "update_failed": "更新失败"}, "login": {"title": "登录到您的账户", "description": "请输入您的API密钥以访问配置面板", "apiKey": "API密钥", "apiKeyPlaceholder": "请输入您的API密钥", "signIn": "登录", "invalidApiKey": "API密钥无效", "configError": "配置未加载", "validating": "正在验证API密钥..."}, "toplevel": {"title": "通用设置", "log": "启用日志", "log_level": "日志级别", "claude_path": "<PERSON> 路径", "host": "主机", "port": "端口", "apikey": "API 密钥", "timeout": "API 超时时间 (毫秒)", "proxy_url": "代理地址", "custom_router_path": "自定义路由脚本路径", "custom_router_path_placeholder": "输入自定义路由脚本文件的绝对路径"}, "transformers": {"title": "自定义转换器", "path": "路径", "project": "项目", "remove": "移除", "add": "添加自定义转换器", "edit": "编辑自定义转换器", "delete": "删除自定义转换器", "delete_transformer_confirm": "您确定要删除此自定义转换器吗？", "parameters": "参数"}, "providers": {"title": "供应商", "name": "名称", "api_base_url": "API 完整地址", "api_key": "API 密钥", "models": "模型", "models_placeholder": "输入模型名称并按回车键添加", "add_model": "添加模型", "select_models": "选择模型", "remove": "移除", "add": "添加供应商", "edit": "编辑供应商", "delete": "删除", "cancel": "取消", "delete_provider_confirm": "您确定要删除此供应商吗？", "test_connectivity": "测试连通性", "testing": "测试中...", "connection_successful": "连接成功！", "connection_failed": "连接失败！", "missing_credentials": "缺少 API 基础地址或 API 密钥", "fetch_available_models": "获取可用模型", "fetching_models": "获取模型中...", "fetch_models_failed": "获取模型失败", "transformers": "转换器", "select_transformer": "选择转换器", "no_transformers": "无可用转换器", "provider_transformer": "供应商转换器", "model_transformers": "模型转换器", "transformer_parameters": "转换器参数", "add_parameter": "添加参数", "parameter_name": "参数名称", "parameter_value": "参数值", "selected_transformers": "已选转换器", "import_from_template": "从模板导入", "no_templates_found": "未找到模板", "select_template": "选择一个模板...", "api_key_required": "API 密钥为必填项", "name_required": "名称为必填项", "name_duplicate": "已存在同名供应商", "search": "搜索供应商..."}, "router": {"title": "路由", "default": "默认", "background": "后台", "think": "思考", "longContext": "长上下文", "longContextThreshold": "上下文阈值", "webSearch": "网络搜索", "image": "图像", "forceUseImageAgent": "强制使用图像代理", "selectModel": "选择一个模型...", "searchModel": "搜索模型...", "noModelFound": "未找到模型."}, "json_editor": {"title": "JSON 编辑器", "save": "保存", "saving": "保存中...", "cancel": "取消", "save_failed": "配置保存失败", "save_and_restart": "保存并重启"}, "statusline": {"title": "状态栏配置", "enable": "启用状态栏", "theme": "主题样式", "theme_default": "默认", "theme_powerline": "Powerline", "modules": "模块", "module_type": "类型", "module_icon": "图标", "module_text": "文本", "module_color": "颜色", "module_background": "背景", "module_text_description": "输入显示文本，可使用变量:", "module_color_description": "选择文字颜色", "module_background_description": "选择背景颜色（可选）", "module_script_path": "脚本路径", "module_script_path_description": "输入Node.js脚本文件的绝对路径", "add_module": "添加模块", "remove_module": "移除模块", "delete_module": "删除组件", "preview": "预览", "components": "组件", "properties": "属性", "workDir": "工作目录", "gitBranch": "Git分支", "model": "模型", "usage": "使用情况", "script": "脚本", "background_none": "无", "color_black": "黑色", "color_red": "红色", "color_green": "绿色", "color_yellow": "黄色", "color_blue": "蓝色", "color_magenta": "品红", "color_cyan": "青色", "color_white": "白色", "color_bright_black": "亮黑色", "color_bright_red": "亮红色", "color_bright_green": "亮绿色", "color_bright_yellow": "亮黄色", "color_bright_blue": "亮蓝色", "color_bright_magenta": "亮品红", "color_bright_cyan": "亮青色", "color_bright_white": "亮白色", "font_placeholder": "选择字体", "theme_placeholder": "选择主题样式", "icon_placeholder": "粘贴图标或输入名称搜索...", "icon_description": "输入图标字符、粘贴图标或搜索图标（可选）", "text_placeholder": "例如: {{workDirName}}", "script_placeholder": "例如: /path/to/your/script.js", "drag_hint": "拖拽组件到此处进行配置", "select_hint": "选择一个组件进行配置", "no_icons_found": "未找到图标", "no_icons_available": "暂无可用图标", "import_export": "导入/导出", "import": "导入配置", "export": "导出配置", "download_template": "下载模板", "import_export_help": "导出当前配置为JSON文件，或从JSON文件导入配置。您也可以下载配置模板作为参考。", "export_success": "配置导出成功", "export_failed": "配置导出失败", "import_success": "配置导入成功", "import_failed": "配置导入失败", "invalid_config": "无效的配置文件", "template_download_success": "模板下载成功", "template_download_success_desc": "配置模板已下载到您的设备", "template_download_failed": "模板下载失败"}, "log_viewer": {"title": "日志查看器", "close": "关闭", "download": "下载", "clear": "清除", "auto_refresh_on": "自动刷新开启", "auto_refresh_off": "自动刷新关闭", "load_failed": "加载日志失败", "no_logs_available": "暂无日志", "logs_cleared": "日志清除成功", "clear_failed": "清除日志失败", "logs_downloaded": "日志下载成功", "back_to_files": "返回文件列表", "select_file": "选择要查看的日志文件", "no_log_files_available": "暂无日志文件", "load_files_failed": "加载日志文件失败", "group_by_req_id": "按请求ID分组", "grouped_on": "已分组", "request_groups": "请求组", "total_requests": "总请求数", "total_logs": "总日志数", "request": "请求", "logs": "条日志", "first_log": "首条日志", "last_log": "末条日志", "back_to_all_logs": "返回所有日志", "worker_error": "Worker错误", "worker_init_failed": "Worker初始化失败", "grouping_not_supported": "服务器不支持日志分组", "back": "返回"}}