{"common": {"yes": "Yes", "no": "No"}, "app": {"title": "Claude <PERSON> Router", "save": "Save", "save_and_restart": "Save and Restart", "cancel": "Cancel", "edit": "Edit", "remove": "Remove", "delete": "Delete", "settings": "Settings", "selectFile": "Select File", "config_saved_success": "Config saved successfully", "config_saved_failed": "Failed to save config", "config_saved_restart_success": "Config saved and service restarted successfully", "config_saved_restart_failed": "Failed to save config and restart service", "new_version_available": "New Version Available", "update_description": "A new version is available. Please review the changelog and update to get the latest features and improvements.", "no_changelog_available": "No changelog available", "later": "Later", "update_now": "Update Now", "no_updates_available": "No updates available", "update_check_failed": "Failed to check for updates", "update_successful": "Update successful", "update_failed": "Update failed"}, "login": {"title": "Sign in to your account", "description": "Enter your API key to access the configuration panel", "apiKey": "API Key", "apiKeyPlaceholder": "Enter your API key", "signIn": "Sign In", "invalidApiKey": "Invalid API key", "configError": "Configuration not loaded", "validating": "Validating API key..."}, "toplevel": {"title": "General Settings", "log": "Enable Logging", "log_level": "Log Level", "claude_path": "<PERSON>", "host": "Host", "port": "Port", "apikey": "API Key", "timeout": "API Timeout (ms)", "proxy_url": "Proxy URL", "custom_router_path": "Custom Router Script Path", "custom_router_path_placeholder": "Enter absolute path to custom router script file"}, "transformers": {"title": "Custom Transformers", "path": "Path", "project": "Project", "remove": "Remove", "add": "Add Custom Transformer", "edit": "Edit Custom Transformer", "delete": "Delete Custom Transformer", "delete_transformer_confirm": "Are you sure you want to delete this custom transformer?", "parameters": "Parameters"}, "providers": {"title": "Providers", "name": "Name", "api_base_url": "API Full URL", "api_key": "API Key", "models": "Models", "models_placeholder": "Enter model name and press Enter to add", "add_model": "Add Model", "select_models": "Select Models", "remove": "Remove", "add": "Add Provider", "edit": "Edit Provider", "delete": "Delete", "cancel": "Cancel", "delete_provider_confirm": "Are you sure you want to delete this provider?", "test_connectivity": "Test Connectivity", "testing": "Testing...", "connection_successful": "Connection successful!", "connection_failed": "Connection failed!", "missing_credentials": "Missing API base URL or API key", "fetch_available_models": "Fetch available models", "fetching_models": "Fetching models...", "fetch_models_failed": "Failed to fetch models", "transformers": "Transformers", "select_transformer": "Select Transformer", "no_transformers": "No transformers available", "provider_transformer": "Provider Transformer", "model_transformers": "Model Transformers", "transformer_parameters": "Transformer Parameters", "add_parameter": "Add Parameter", "parameter_name": "Parameter Name", "parameter_value": "Parameter Value", "selected_transformers": "Selected Transformers", "import_from_template": "Import from template", "no_templates_found": "No templates found", "select_template": "Select a template...", "api_key_required": "API Key is required", "name_required": "Name is required", "name_duplicate": "A provider with this name already exists", "search": "Search providers..."}, "router": {"title": "Router", "default": "<PERSON><PERSON><PERSON>", "background": "Background", "think": "Think", "longContext": "Long Context", "longContextThreshold": "Context Threshold", "webSearch": "Web Search", "image": "Image", "forceUseImageAgent": "Force Use Image Agent", "selectModel": "Select a model...", "searchModel": "Search model...", "noModelFound": "No model found."}, "json_editor": {"title": "JSON Editor", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "save_failed": "Failed to save config", "save_and_restart": "Save & Restart"}, "statusline": {"title": "Status Line Configuration", "enable": "Enable Status Line", "theme": "Theme Style", "theme_default": "<PERSON><PERSON><PERSON>", "theme_powerline": "Powerline", "modules": "<PERSON><PERSON><PERSON>", "module_type": "Type", "module_icon": "Icon", "module_text": "Text", "module_color": "Color", "module_background": "Background", "module_text_description": "Enter display text, variables can be used:", "module_color_description": "Select text color", "module_background_description": "Select background color (optional)", "module_script_path": "Script Path", "module_script_path_description": "Enter the absolute path of the Node.js script file", "add_module": "<PERSON><PERSON>", "remove_module": "Remove <PERSON>", "delete_module": "Delete Module", "preview": "Preview", "components": "Components", "properties": "Properties", "workDir": "Working Directory", "gitBranch": "Git Branch", "model": "Model", "usage": "Usage", "script": "<PERSON><PERSON><PERSON>", "background_none": "None", "color_black": "Black", "color_red": "Red", "color_green": "Green", "color_yellow": "Yellow", "color_blue": "Blue", "color_magenta": "Ma<PERSON><PERSON>", "color_cyan": "<PERSON><PERSON>", "color_white": "White", "color_bright_black": "<PERSON>", "color_bright_red": "Bright Red", "color_bright_green": "Bright Green", "color_bright_yellow": "Bright Yellow", "color_bright_blue": "Bright Blue", "color_bright_magenta": "<PERSON>", "color_bright_cyan": "<PERSON>", "color_bright_white": "<PERSON>", "font_placeholder": "Select Font", "theme_placeholder": "Select Theme Style", "icon_placeholder": "Paste icon or search by name...", "icon_description": "Enter icon character, paste icon, or search icons (optional)", "text_placeholder": "e.g.: {{workDirName}}", "script_placeholder": "e.g.: /path/to/your/script.js", "drag_hint": "Drag components here to configure", "select_hint": "Select a component to configure", "no_icons_found": "No icons found", "no_icons_available": "No icons available", "import_export": "Import/Export", "import": "Import Config", "export": "Export Config", "download_template": "Download Template", "import_export_help": "Export current configuration as a JSON file, or import configuration from a JSON file. You can also download a configuration template for reference.", "export_success": "Configuration exported successfully", "export_failed": "Failed to export configuration", "import_success": "Configuration imported successfully", "import_failed": "Failed to import configuration", "invalid_config": "Invalid configuration file", "template_download_success": "Template downloaded successfully", "template_download_success_desc": "Configuration template has been downloaded to your device", "template_download_failed": "Failed to download template"}, "log_viewer": {"title": "Log Viewer", "close": "Close", "download": "Download", "clear": "Clear", "auto_refresh_on": "Auto Refresh On", "auto_refresh_off": "Auto Refresh Off", "load_failed": "Failed to load logs", "no_logs_available": "No logs available", "logs_cleared": "Logs cleared successfully", "clear_failed": "Failed to clear logs", "logs_downloaded": "Logs downloaded successfully", "back_to_files": "Back to Files", "select_file": "Select a log file to view", "no_log_files_available": "No log files available", "load_files_failed": "Failed to load log files", "group_by_req_id": "Group by Request ID", "grouped_on": "Grouped", "request_groups": "Request Groups", "total_requests": "Total Requests", "total_logs": "Total Logs", "request": "Request", "logs": "logs", "first_log": "First Log", "last_log": "Last Log", "back_to_all_logs": "Back to All Logs", "worker_error": "Worker error", "worker_init_failed": "Failed to initialize worker", "grouping_not_supported": "Log grouping not supported by server", "back": "Back"}}