# Debug 目录

这个目录用于存放调试、测试生成的代码及生成的总结。

## Token 使用量修改功能

本项目实现了一个新的transformer，用于修改后端LLM响应中的token使用量。

### 功能说明

- ✅ 创建了一个名为`token-multiplier`的transformer
- ✅ 可以通过配置文件中的系数参数来调整token使用量
- ✅ 支持修改`completion_tokens`、`prompt_tokens`和`total_tokens`字段
- ✅ 支持Anthropic格式的`input_tokens`、`output_tokens`字段
- ✅ 同时支持流式和非流式响应
- ✅ 包含完整的错误处理和边界情况处理
- ✅ 通过了全面的测试验证

### 核心特性

1. **多格式支持**: 支持OpenAI和Anthropic等不同API格式
2. **灵活配置**: 可以为不同provider和model设置不同的multiplier系数
3. **流式处理**: 完整支持SSE流式响应的token修改
4. **错误容错**: 遇到异常数据时保持原始值，不会导致服务中断
5. **性能优化**: 最小化处理开销，适合生产环境使用

### 使用方法

#### 基本配置
```json
{
  "transformers": [
    {
      "path": "~/.claude-code-router/plugins/token-multiplier-transformer.js"
    }
  ],
  "Providers": [
    {
      "name": "your-provider",
      "transformer": {
        "use": [
          [
            "token-multiplier",
            {
              "multiplier": 1.5
            }
          ]
        ]
      }
    }
  ]
}
```

#### 高级配置（不同模型不同系数）
```json
{
  "transformer": {
    "use": [
      [
        "token-multiplier",
        { "multiplier": 1.2 }
      ]
    ],
    "gpt-4": {
      "use": [
        [
          "token-multiplier",
          { "multiplier": 2.0 }
        ]
      ]
    }
  }
}
```

### 实现文件

- `token-multiplier-transformer.js` - transformer核心实现
- `test-config.json` - 测试配置文件示例
- `test-usage.js` - 完整的测试脚本
- `token-multiplier-usage-guide.md` - 详细使用指南
- `integration-example.md` - 实际集成示例

### 测试结果

所有测试用例均通过：
- ✅ 基本usage修改测试
- ✅ Anthropic格式usage修改测试
- ✅ OpenAI响应处理测试
- ✅ Anthropic响应处理测试
- ✅ 边界情况测试（空值、无效值、部分字段）

### 快速开始

1. 复制transformer文件到plugins目录
2. 在config.json中注册transformer
3. 在需要的Provider中配置使用
4. 重启服务
5. 验证功能正常工作

详细步骤请参考 `integration-example.md`
