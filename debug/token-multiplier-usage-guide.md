# Token Multiplier Transformer 使用指南

## 概述

Token Multiplier Transformer 是一个自定义transformer，用于修改后端LLM响应中的token使用量。它可以将返回的token数量乘以指定的系数，适用于需要调整token计费或统计的场景。

## 功能特性

- ✅ 支持修改 `completion_tokens`、`prompt_tokens`、`total_tokens` 字段
- ✅ 支持 Anthropic 格式的 `input_tokens`、`output_tokens` 字段
- ✅ 支持流式和非流式响应
- ✅ 支持多种LLM提供商格式
- ✅ 自动处理边界情况和错误

## 安装和配置

### 1. 复制transformer文件

将 `token-multiplier-transformer.js` 文件复制到你的项目目录中，建议放在 `~/.claude-code-router/plugins/` 目录下。

### 2. 在配置文件中注册transformer

在 `~/.claude-code-router/config.json` 中添加transformer定义：

```json
{
  "transformers": [
    {
      "path": "/path/to/token-multiplier-transformer.js",
      "options": {
        "multiplier": 1.5
      }
    }
  ]
}
```

### 3. 在Provider中使用transformer

在需要修改token使用量的Provider配置中添加transformer：

```json
{
  "Providers": [
    {
      "name": "your-provider",
      "api_base_url": "https://api.example.com/v1/chat/completions",
      "api_key": "your-api-key",
      "models": ["your-model"],
      "transformer": {
        "use": [
          [
            "token-multiplier",
            {
              "multiplier": 2.0
            }
          ]
        ]
      }
    }
  ]
}
```

## 配置参数

### multiplier (必需)

- **类型**: `number`
- **默认值**: `1.0`
- **说明**: token使用量的乘数系数
- **示例**: 
  - `1.5` - 将token数量增加50%
  - `2.0` - 将token数量翻倍
  - `0.8` - 将token数量减少20%

## 使用示例

### 示例1: 基本使用

```json
{
  "name": "openai-provider",
  "api_base_url": "https://api.openai.com/v1/chat/completions",
  "api_key": "sk-xxx",
  "models": ["gpt-3.5-turbo", "gpt-4"],
  "transformer": {
    "use": [
      [
        "token-multiplier",
        {
          "multiplier": 1.5
        }
      ]
    ]
  }
}
```

### 示例2: 与其他transformer组合使用

```json
{
  "name": "anthropic-provider",
  "api_base_url": "https://api.anthropic.com/v1/messages",
  "api_key": "sk-ant-xxx",
  "models": ["claude-3-sonnet"],
  "transformer": {
    "use": [
      "anthropic",
      [
        "token-multiplier",
        {
          "multiplier": 1.8
        }
      ]
    ]
  }
}
```

### 示例3: 不同模型使用不同系数

```json
{
  "name": "multi-model-provider",
  "api_base_url": "https://api.example.com/v1/chat/completions",
  "api_key": "sk-xxx",
  "models": ["model-a", "model-b"],
  "transformer": {
    "use": [
      [
        "token-multiplier",
        {
          "multiplier": 1.2
        }
      ]
    ],
    "model-a": {
      "use": [
        [
          "token-multiplier",
          {
            "multiplier": 1.5
          }
        ]
      ]
    },
    "model-b": {
      "use": [
        [
          "token-multiplier",
          {
            "multiplier": 2.0
          }
        ]
      ]
    }
  }
}
```

## 支持的响应格式

### OpenAI 格式

```json
{
  "usage": {
    "prompt_tokens": 100,
    "completion_tokens": 50,
    "total_tokens": 150
  }
}
```

### Anthropic 格式

```json
{
  "usage": {
    "input_tokens": 288,
    "output_tokens": 503
  }
}
```

## 测试

运行测试脚本验证功能：

```bash
cd debug
node test-usage.js
```

## 注意事项

1. **系数设置**: 建议根据实际需求合理设置multiplier值，避免过大或过小的值
2. **精度处理**: token数量会被四舍五入到最近的整数
3. **兼容性**: 支持各种LLM提供商的响应格式
4. **错误处理**: 当遇到无效数据时，会保持原始值不变
5. **性能影响**: 对响应处理有轻微的性能开销，但通常可以忽略

## 故障排除

### 问题1: transformer未生效

**解决方案**:
- 检查transformer文件路径是否正确
- 确认配置文件语法正确
- 查看日志输出确认transformer是否被加载

### 问题2: token数量未改变

**解决方案**:
- 确认multiplier参数设置正确
- 检查响应格式是否包含usage字段
- 查看控制台日志确认处理过程

### 问题3: 流式响应处理异常

**解决方案**:
- 确认使用的是最新版本的transformer
- 检查SSE数据格式是否标准
- 查看错误日志定位具体问题

## 更新日志

- **v1.0.0**: 初始版本，支持基本的token使用量修改功能
- 支持OpenAI和Anthropic格式
- 支持流式和非流式响应
- 包含完整的测试用例
