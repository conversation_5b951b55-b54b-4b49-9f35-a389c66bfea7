# Token Multiplier Transformer 集成示例

## 实际集成步骤

### 1. 将transformer文件放置到正确位置

```bash
# 创建plugins目录
mkdir -p ~/.claude-code-router/plugins

# 复制transformer文件
cp debug/token-multiplier-transformer.js ~/.claude-code-router/plugins/
```

### 2. 修改现有配置文件

假设你的 `~/.claude-code-router/config.json` 当前配置如下：

```json
{
  "LOG": true,
  "HOST": "127.0.0.1",
  "PORT": 8080,
  "APIKEY": "your-secret-key",
  "API_TIMEOUT_MS": 600000,
  "Providers": [
    {
      "name": "openrouter",
      "api_base_url": "https://openrouter.ai/api/v1/chat/completions",
      "api_key": "sk-or-xxx",
      "models": [
        "google/gemini-2.5-pro-preview",
        "anthropic/claude-sonnet-4"
      ],
      "transformer": {
        "use": ["openrouter"]
      }
    },
    {
      "name": "deepseek",
      "api_base_url": "https://api.deepseek.com/chat/completions",
      "api_key": "sk-xxx",
      "models": ["deepseek-chat"],
      "transformer": {
        "use": ["deepseek"]
      }
    }
  ],
  "Router": {
    "default": "openrouter,google/gemini-2.5-pro-preview"
  }
}
```

### 3. 添加token multiplier transformer

修改配置文件，添加transformer定义和使用：

```json
{
  "LOG": true,
  "HOST": "127.0.0.1",
  "PORT": 8080,
  "APIKEY": "your-secret-key",
  "API_TIMEOUT_MS": 600000,
  "transformers": [
    {
      "path": "/Users/<USER>/.claude-code-router/plugins/token-multiplier-transformer.js",
      "options": {
        "multiplier": 1.5
      }
    }
  ],
  "Providers": [
    {
      "name": "openrouter",
      "api_base_url": "https://openrouter.ai/api/v1/chat/completions",
      "api_key": "sk-or-xxx",
      "models": [
        "google/gemini-2.5-pro-preview",
        "anthropic/claude-sonnet-4"
      ],
      "transformer": {
        "use": [
          "openrouter",
          [
            "token-multiplier",
            {
              "multiplier": 2.0
            }
          ]
        ]
      }
    },
    {
      "name": "deepseek",
      "api_base_url": "https://api.deepseek.com/chat/completions",
      "api_key": "sk-xxx",
      "models": ["deepseek-chat"],
      "transformer": {
        "use": [
          "deepseek",
          [
            "token-multiplier",
            {
              "multiplier": 1.5
            }
          ]
        ]
      }
    }
  ],
  "Router": {
    "default": "openrouter,google/gemini-2.5-pro-preview"
  }
}
```

### 4. 重启服务

```bash
# 停止现有服务
ccr stop

# 启动服务
ccr start
```

## 验证功能

### 1. 检查日志

启动服务后，查看日志确认transformer被正确加载：

```bash
# 查看日志
tail -f ~/.claude-code-router/logs/app.log
```

你应该能看到类似的日志：

```
TokenMultiplierTransformer initialized with multiplier: 2
TokenMultiplierTransformer initialized with multiplier: 1.5
```

### 2. 发送测试请求

使用Claude Code或直接API调用测试：

```bash
# 使用Claude Code测试
ccr code

# 或者直接API调用
curl -X POST http://localhost:8080/v1/messages \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-secret-key" \
  -d '{
    "model": "google/gemini-2.5-pro-preview",
    "max_tokens": 100,
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ]
  }'
```

### 3. 检查响应

在响应中，你应该能看到token使用量被修改了。例如：

**原始响应可能是**：
```json
{
  "usage": {
    "prompt_tokens": 10,
    "completion_tokens": 15,
    "total_tokens": 25
  }
}
```

**修改后的响应（multiplier=2.0）**：
```json
{
  "usage": {
    "prompt_tokens": 20,
    "completion_tokens": 30,
    "total_tokens": 50
  }
}
```

## 高级配置示例

### 不同模型使用不同系数

```json
{
  "name": "openrouter",
  "transformer": {
    "use": [
      "openrouter",
      [
        "token-multiplier",
        {
          "multiplier": 1.2
        }
      ]
    ],
    "google/gemini-2.5-pro-preview": {
      "use": [
        "openrouter",
        [
          "token-multiplier",
          {
            "multiplier": 1.8
          }
        ]
      ]
    },
    "anthropic/claude-sonnet-4": {
      "use": [
        "openrouter",
        [
          "token-multiplier",
          {
            "multiplier": 1.5
          }
        ]
      ]
    }
  }
}
```

### 与其他transformer组合

```json
{
  "name": "complex-provider",
  "transformer": {
    "use": [
      [
        "maxtoken",
        {
          "max_tokens": 4096
        }
      ],
      "deepseek",
      [
        "token-multiplier",
        {
          "multiplier": 1.3
        }
      ]
    ]
  }
}
```

## 故障排除

### 常见问题

1. **transformer未加载**
   - 检查文件路径是否正确
   - 确认文件权限可读
   - 查看启动日志

2. **token未修改**
   - 确认transformer在正确的位置
   - 检查multiplier参数
   - 查看处理日志

3. **服务启动失败**
   - 检查配置文件JSON语法
   - 确认所有路径存在
   - 查看错误日志

### 调试技巧

1. **启用详细日志**：
   ```json
   {
     "LOG": true,
     "LOG_LEVEL": "debug"
   }
   ```

2. **测试transformer**：
   ```bash
   cd debug
   node test-usage.js
   ```

3. **检查配置**：
   ```bash
   ccr config
   ```

## 性能考虑

- Token multiplier transformer的性能开销很小
- 主要开销在JSON解析和修改
- 对于高并发场景，建议监控性能指标
- 可以通过调整multiplier值来平衡功能需求和性能
