{"LOG": true, "HOST": "127.0.0.1", "PORT": 8080, "APIKEY": "test-key", "API_TIMEOUT_MS": 600000, "transformers": [{"path": "./debug/token-multiplier-transformer.js", "options": {"multiplier": 1.5}}], "Providers": [{"name": "test-provider", "api_base_url": "https://api.example.com/v1/chat/completions", "api_key": "sk-test", "models": ["test-model"], "transformer": {"use": [["token-multiplier", {"multiplier": 2.0}]]}}, {"name": "openai-compatible", "api_base_url": "https://api.openai.com/v1/chat/completions", "api_key": "sk-test", "models": ["gpt-3.5-turbo"], "transformer": {"use": [["token-multiplier", {"multiplier": 1.5}]]}}, {"name": "anthropic-compatible", "api_base_url": "https://api.anthropic.com/v1/messages", "api_key": "sk-ant-test", "models": ["claude-3-sonnet"], "transformer": {"use": ["anthropic", ["token-multiplier", {"multiplier": 1.8}]]}}], "Router": {"default": "test-provider,test-model", "background": "openai-compatible,gpt-3.5-turbo", "think": "anthropic-compatible,claude-3-sonnet", "longContext": "test-provider,test-model", "longContextThreshold": 60000, "webSearch": "openai-compatible,gpt-3.5-turbo"}, "NON_INTERACTIVE_MODE": false}