/**
 * Simple Token Multiplier Transformer
 * 简化版本用于测试
 */

module.exports = class SimpleTokenMultiplier {
  constructor(options = {}) {
    this.name = "token-multiplier";
    this.multiplier = options.multiplier || 1.0;
    console.log(`SimpleTokenMultiplier initialized with multiplier: ${this.multiplier}`);
  }

  /**
   * 修改usage对象中的token数量
   */
  multiplyUsage(usage) {
    if (!usage || typeof usage !== 'object') {
      return usage;
    }

    const modifiedUsage = { ...usage };

    // 修改各种token字段
    if (typeof usage.completion_tokens === 'number') {
      modifiedUsage.completion_tokens = Math.round(usage.completion_tokens * this.multiplier);
    }
    
    if (typeof usage.prompt_tokens === 'number') {
      modifiedUsage.prompt_tokens = Math.round(usage.prompt_tokens * this.multiplier);
    }
    
    if (typeof usage.total_tokens === 'number') {
      modifiedUsage.total_tokens = Math.round(usage.total_tokens * this.multiplier);
    }

    // 支持Anthropic格式
    if (typeof usage.input_tokens === 'number') {
      modifiedUsage.input_tokens = Math.round(usage.input_tokens * this.multiplier);
    }
    
    if (typeof usage.output_tokens === 'number') {
      modifiedUsage.output_tokens = Math.round(usage.output_tokens * this.multiplier);
    }

    console.log(`Token usage modified: ${JSON.stringify(usage)} -> ${JSON.stringify(modifiedUsage)}`);
    return modifiedUsage;
  }

  /**
   * 处理非流式响应
   */
  async transformResponseOut(response) {
    // 只处理JSON响应
    if (!response.headers.get("Content-Type")?.includes("application/json")) {
      return response;
    }

    try {
      const jsonResponse = await response.json();
      
      // 修改usage字段
      if (jsonResponse.usage) {
        jsonResponse.usage = this.multiplyUsage(jsonResponse.usage);
      }

      // 创建新的响应
      return new Response(JSON.stringify(jsonResponse), {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      });
    } catch (error) {
      console.error("Error processing response:", error);
      return response;
    }
  }
};
